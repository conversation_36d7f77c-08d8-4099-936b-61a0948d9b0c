["tests/test_fetcher.py::TestConvenienceFunctions::test_fetch_url", "tests/test_fetcher.py::TestConvenienceFunctions::test_fetch_urls", "tests/test_fetcher.py::TestErrorScenarios::test_connection_error", "tests/test_fetcher.py::TestErrorScenarios::test_lazy_session_initialization", "tests/test_fetcher.py::TestErrorScenarios::test_session_not_initialized", "tests/test_fetcher.py::TestWebFetcher::test_content_parsing_html", "tests/test_fetcher.py::TestWebFetcher::test_content_parsing_json_error", "tests/test_fetcher.py::TestWebFetcher::test_content_parsing_text", "tests/test_fetcher.py::TestWebFetcher::test_context_manager", "tests/test_fetcher.py::TestWebFetcher::test_custom_headers", "tests/test_fetcher.py::TestWebFetcher::test_fetch_batch", "tests/test_fetcher.py::TestWebFetcher::test_fetch_single_http_error", "tests/test_fetcher.py::TestWebFetcher::test_fetch_single_success", "tests/test_fetcher.py::TestWebFetcher::test_fetch_single_with_retries", "tests/test_fetcher.py::TestWebFetcher::test_post_request_with_data", "tests/test_fetcher.py::TestWebFetcher::test_response_size_limit", "tests/test_fetcher.py::TestWebFetcher::test_session_creation", "tests/test_fetcher.py::TestWebFetcher::test_timeout_override", "tests/test_models.py::TestBatchFetchRequest::test_duplicate_urls", "tests/test_models.py::TestBatchFetchRequest::test_empty_batch", "tests/test_models.py::TestBatchFetchRequest::test_too_many_requests", "tests/test_models.py::TestBatchFetchRequest::test_valid_batch", "tests/test_models.py::TestBatchFetchResult::test_from_results", "tests/test_models.py::TestBatchFetchResult::test_success_rate_calculation", "tests/test_models.py::TestFetchConfig::test_custom_config", "tests/test_models.py::TestFetchConfig::test_default_config", "tests/test_models.py::TestFetchConfig::test_validation_concurrent_requests", "tests/test_models.py::TestFetchConfig::test_validation_positive_timeout", "tests/test_models.py::TestFetchConfig::test_validation_retries", "tests/test_models.py::TestFetchRequest::test_custom_request", "tests/test_models.py::TestFetchRequest::test_method_validation", "tests/test_models.py::TestFetchRequest::test_url_validation", "tests/test_models.py::TestFetchRequest::test_valid_request", "tests/test_models.py::TestFetchResult::test_client_error_result", "tests/test_models.py::TestFetchResult::test_server_error_result", "tests/test_models.py::TestFetchResult::test_successful_result", "tests/test_models.py::TestRequestHeaders::test_custom_headers", "tests/test_models.py::TestRequestHeaders::test_default_headers", "tests/test_models.py::TestRequestHeaders::test_immutable", "tests/test_streaming.py::TestDownloadFileFunction::test_download_file_success", "tests/test_streaming.py::TestDownloadFileFunction::test_download_file_with_progress", "tests/test_streaming.py::TestProgressInfo::test_completion_check", "tests/test_streaming.py::TestProgressInfo::test_progress_properties", "tests/test_streaming.py::TestProgressInfo::test_speed_formatting", "tests/test_streaming.py::TestStreamRequest::test_custom_streaming_config", "tests/test_streaming.py::TestStreamRequest::test_stream_request_with_file", "tests/test_streaming.py::TestStreamRequest::test_valid_stream_request", "tests/test_streaming.py::TestStreamingConfig::test_custom_config", "tests/test_streaming.py::TestStreamingConfig::test_default_config", "tests/test_streaming.py::TestStreamingConfig::test_validation", "tests/test_streaming.py::TestStreamingWebFetcher::test_stream_fetch_file_size_limit", "tests/test_streaming.py::TestStreamingWebFetcher::test_stream_fetch_success", "tests/test_streaming.py::TestStreamingWebFetcher::test_stream_fetch_with_file_output", "tests/test_streaming.py::TestStreamingWebFetcher::test_stream_fetch_with_progress_callback", "tests/test_streaming.py::TestStreamingWebFetcher::test_stream_fetch_without_content_length", "tests/test_utils.py::TestIntegrationScenarios::test_response_analysis_workflow", "tests/test_utils.py::TestIntegrationScenarios::test_url_validation_and_normalization_workflow", "tests/test_utils.py::TestRateLimiter::test_per_host_rate_limiting", "tests/test_utils.py::TestRateLimiter::test_rate_limiter_stats", "tests/test_utils.py::TestRateLimiter::test_rate_limiting_basic", "tests/test_utils.py::TestResponseAnalyzer::test_analyze_headers", "tests/test_utils.py::TestResponseAnalyzer::test_detect_content_type", "tests/test_utils.py::TestSimpleCache::test_cache_basic_operations", "tests/test_utils.py::TestSimpleCache::test_cache_compression", "tests/test_utils.py::TestSimpleCache::test_cache_expiration", "tests/test_utils.py::TestSimpleCache::test_cache_lru_eviction", "tests/test_utils.py::TestSimpleCache::test_cache_size_and_clear", "tests/test_utils.py::TestURLValidator::test_analyze_url", "tests/test_utils.py::TestURLValidator::test_domain_validation", "tests/test_utils.py::TestURLValidator::test_is_valid_url", "tests/test_utils.py::TestURLValidator::test_normalize_url"]