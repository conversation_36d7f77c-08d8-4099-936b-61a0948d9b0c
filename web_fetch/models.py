"""
Data models and configuration classes for the web fetcher utility.

This module defines Pydantic models and dataclasses for structured data handling,
leveraging modern Python 3.11+ features including type hints, Union types, and Optional.
"""

from __future__ import annotations

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union
from urllib.parse import urlparse

from pydantic import BaseModel, Field, HttpUrl, field_validator, ConfigDict


class ContentType(str, Enum):
    """Enumeration of supported content types for fetched data."""
    
    RAW = "raw"
    JSON = "json"
    HTML = "html"
    TEXT = "text"


class RetryStrategy(str, Enum):
    """Enumeration of retry strategies for failed requests."""
    
    NONE = "none"
    LINEAR = "linear"
    EXPONENTIAL = "exponential"


@dataclass(frozen=True)
class RequestHeaders:
    """Immutable dataclass for HTTP request headers with common defaults."""
    
    user_agent: str = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )
    accept: str = "*/*"
    accept_language: str = "en-US,en;q=0.9"
    accept_encoding: str = "gzip, deflate, br"
    connection: str = "keep-alive"
    custom_headers: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, str]:
        """Convert headers to dictionary format for aiohttp."""
        headers = {
            "User-Agent": self.user_agent,
            "Accept": self.accept,
            "Accept-Language": self.accept_language,
            "Accept-Encoding": self.accept_encoding,
            "Connection": self.connection,
        }
        headers.update(self.custom_headers)
        return headers


class FetchConfig(BaseModel):
    """Configuration model for web fetching operations using Pydantic."""
    
    # Timeout settings
    total_timeout: float = Field(default=30.0, gt=0, description="Total request timeout in seconds")
    connect_timeout: float = Field(default=10.0, gt=0, description="Connection timeout in seconds")
    read_timeout: float = Field(default=20.0, gt=0, description="Read timeout in seconds")
    
    # Concurrency settings
    max_concurrent_requests: int = Field(default=10, ge=1, le=100, description="Maximum concurrent requests")
    max_connections_per_host: int = Field(default=5, ge=1, le=20, description="Max connections per host")
    
    # Retry settings
    retry_strategy: RetryStrategy = Field(default=RetryStrategy.EXPONENTIAL)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1, le=60.0)
    
    # Content settings
    max_response_size: int = Field(default=10 * 1024 * 1024, gt=0, description="Max response size in bytes")
    follow_redirects: bool = Field(default=True)
    verify_ssl: bool = Field(default=True)
    
    # Headers
    headers: RequestHeaders = Field(default_factory=RequestHeaders)
    
    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True
    )


class FetchRequest(BaseModel):
    """Model representing a single fetch request."""
    
    url: HttpUrl = Field(description="URL to fetch")
    method: str = Field(default="GET", pattern=r"^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)$")
    headers: Optional[Dict[str, str]] = Field(default=None)
    data: Optional[Union[str, bytes, Dict[str, Any]]] = Field(default=None)
    params: Optional[Dict[str, str]] = Field(default=None)
    content_type: ContentType = Field(default=ContentType.RAW)
    timeout_override: Optional[float] = Field(default=None, gt=0)
    
    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        """Validate URL format and scheme."""
        parsed = urlparse(str(v))
        if parsed.scheme not in ('http', 'https'):
            raise ValueError('URL must use http or https scheme')
        return v
    
    model_config = ConfigDict(
        use_enum_values=True
    )


@dataclass
class FetchResult:
    """Dataclass representing the result of a fetch operation."""
    
    url: str
    status_code: int
    headers: Dict[str, str]
    content: Union[str, bytes, Dict[str, Any], None]
    content_type: ContentType
    response_time: float
    timestamp: datetime
    error: Optional[str] = None
    retry_count: int = 0
    
    @property
    def is_success(self) -> bool:
        """Check if the request was successful."""
        return 200 <= self.status_code < 300 and self.error is None
    
    @property
    def is_client_error(self) -> bool:
        """Check if the request resulted in a client error (4xx)."""
        return 400 <= self.status_code < 500
    
    @property
    def is_server_error(self) -> bool:
        """Check if the request resulted in a server error (5xx)."""
        return 500 <= self.status_code < 600


class BatchFetchRequest(BaseModel):
    """Model for batch fetching multiple URLs."""
    
    requests: List[FetchRequest] = Field(min_length=1, max_length=1000)
    config: Optional[FetchConfig] = Field(default=None)
    
    @field_validator('requests')
    @classmethod
    def validate_unique_urls(cls, v):
        """Ensure URLs are unique in batch request."""
        urls = [str(req.url) for req in v]
        if len(urls) != len(set(urls)):
            raise ValueError('Duplicate URLs found in batch request')
        return v


@dataclass
class BatchFetchResult:
    """Dataclass representing results from a batch fetch operation."""
    
    results: List[FetchResult]
    total_requests: int
    successful_requests: int
    failed_requests: int
    total_time: float
    timestamp: datetime
    
    @classmethod
    def from_results(cls, results: List[FetchResult], total_time: float) -> BatchFetchResult:
        """Create BatchFetchResult from a list of individual results."""
        successful = sum(1 for r in results if r.is_success)
        failed = len(results) - successful
        
        return cls(
            results=results,
            total_requests=len(results),
            successful_requests=successful,
            failed_requests=failed,
            total_time=total_time,
            timestamp=datetime.now()
        )
    
    @property
    def success_rate(self) -> float:
        """Calculate the success rate as a percentage."""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100


# Streaming and utility models

class StreamingConfig(BaseModel):
    """Configuration for streaming operations."""

    chunk_size: int = Field(default=8192, ge=1024, le=1024*1024, description="Chunk size in bytes")
    buffer_size: int = Field(default=64*1024, ge=8192, description="Buffer size for streaming")
    enable_progress: bool = Field(default=True, description="Enable progress tracking")
    progress_interval: float = Field(default=0.1, ge=0.01, le=5.0, description="Progress callback interval in seconds")
    max_file_size: Optional[int] = Field(default=None, ge=0, description="Maximum file size in bytes")

    model_config = ConfigDict(
        validate_assignment=True
    )


@dataclass
class ProgressInfo:
    """Progress information for streaming operations."""

    bytes_downloaded: int
    total_bytes: Optional[int]
    chunk_count: int
    elapsed_time: float
    download_speed: float  # bytes per second
    eta: Optional[float]  # estimated time remaining in seconds
    percentage: Optional[float]  # completion percentage

    @property
    def is_complete(self) -> bool:
        """Check if download is complete."""
        if self.total_bytes is None:
            return False
        return self.bytes_downloaded >= self.total_bytes

    @property
    def speed_human(self) -> str:
        """Human-readable download speed."""
        if self.download_speed < 1024:
            return f"{self.download_speed:.1f} B/s"
        elif self.download_speed < 1024 * 1024:
            return f"{self.download_speed / 1024:.1f} KB/s"
        else:
            return f"{self.download_speed / (1024 * 1024):.1f} MB/s"


class StreamRequest(BaseModel):
    """Request model for streaming operations."""

    url: HttpUrl = Field(description="URL to stream")
    method: str = Field(default="GET", pattern=r"^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)$")
    headers: Optional[Dict[str, str]] = Field(default=None)
    data: Optional[Union[str, bytes, Dict[str, Any]]] = Field(default=None)
    output_path: Optional[Path] = Field(default=None, description="Path to save streamed content")
    streaming_config: StreamingConfig = Field(default_factory=StreamingConfig)
    timeout_override: Optional[float] = Field(default=None, gt=0)

    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        """Validate URL format and scheme."""
        parsed = urlparse(str(v))
        if parsed.scheme not in ('http', 'https'):
            raise ValueError('URL must use http or https scheme')
        return v

    model_config = ConfigDict(
        use_enum_values=True
    )


@dataclass
class StreamResult:
    """Result of a streaming operation."""

    url: str
    status_code: int
    headers: Dict[str, str]
    bytes_downloaded: int
    total_bytes: Optional[int]
    output_path: Optional[Path]
    response_time: float
    timestamp: datetime
    error: Optional[str] = None
    progress_info: Optional[ProgressInfo] = None

    @property
    def is_success(self) -> bool:
        """Check if the streaming operation was successful."""
        return 200 <= self.status_code < 300 and self.error is None

    @property
    def download_complete(self) -> bool:
        """Check if download completed successfully."""
        if not self.is_success or self.total_bytes is None:
            return False
        return self.bytes_downloaded >= self.total_bytes


# Cache models

class CacheConfig(BaseModel):
    """Configuration for caching operations."""

    max_size: int = Field(default=100, ge=1, le=10000, description="Maximum cache entries")
    ttl_seconds: int = Field(default=300, ge=1, description="Time to live in seconds")
    enable_compression: bool = Field(default=True, description="Enable response compression in cache")
    cache_headers: bool = Field(default=True, description="Cache response headers")

    model_config = ConfigDict(
        validate_assignment=True
    )


@dataclass
class CacheEntry:
    """Cache entry for storing responses."""

    url: str
    response_data: Any
    headers: Dict[str, str]
    status_code: int
    timestamp: datetime
    ttl: timedelta
    compressed: bool = False

    @property
    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        return datetime.now() > (self.timestamp + self.ttl)

    @property
    def age_seconds(self) -> float:
        """Get age of cache entry in seconds."""
        return (datetime.now() - self.timestamp).total_seconds()

    def get_data(self) -> Any:
        """Get the response data, decompressing if necessary."""
        if self.compressed and isinstance(self.response_data, bytes):
            import gzip
            try:
                decompressed = gzip.decompress(self.response_data)
                return decompressed.decode('utf-8')
            except Exception:
                return self.response_data
        return self.response_data


# Rate limiting models

class RateLimitConfig(BaseModel):
    """Configuration for rate limiting."""

    requests_per_second: float = Field(default=10.0, gt=0, le=1000, description="Requests per second limit")
    burst_size: int = Field(default=10, ge=1, le=100, description="Burst size for token bucket")
    per_host: bool = Field(default=True, description="Apply rate limiting per host")
    respect_retry_after: bool = Field(default=True, description="Respect Retry-After headers")

    model_config = ConfigDict(
        validate_assignment=True
    )


@dataclass
class RateLimitState:
    """State for rate limiting implementation."""

    tokens: float
    last_update: datetime
    requests_made: int = 0

    def can_make_request(self, config: RateLimitConfig) -> bool:
        """Check if a request can be made based on rate limit."""
        now = datetime.now()
        time_passed = (now - self.last_update).total_seconds()

        # Add tokens based on time passed
        self.tokens = min(
            config.burst_size,
            self.tokens + (time_passed * config.requests_per_second)
        )
        self.last_update = now

        return self.tokens >= 1.0

    def consume_token(self) -> None:
        """Consume a token for making a request."""
        self.tokens = max(0, self.tokens - 1.0)
        self.requests_made += 1


# URL validation and analysis models

@dataclass
class URLAnalysis:
    """Analysis result for URL validation and normalization."""

    original_url: str
    normalized_url: str
    is_valid: bool
    scheme: str
    domain: str
    path: str
    query_params: Dict[str, str]
    fragment: str
    port: Optional[int]
    is_secure: bool
    issues: List[str] = field(default_factory=list)

    @property
    def is_http(self) -> bool:
        """Check if URL uses HTTP protocol."""
        return self.scheme.lower() in ('http', 'https')

    @property
    def is_local(self) -> bool:
        """Check if URL points to localhost."""
        return self.domain.lower() in ('localhost', '127.0.0.1', '::1')


@dataclass
class HeaderAnalysis:
    """Analysis of HTTP response headers."""

    content_type: Optional[str]
    content_length: Optional[int]
    content_encoding: Optional[str]
    server: Optional[str]
    cache_control: Optional[str]
    etag: Optional[str]
    last_modified: Optional[str]
    expires: Optional[str]
    security_headers: Dict[str, str]
    custom_headers: Dict[str, str]

    @property
    def is_cacheable(self) -> bool:
        """Check if response appears to be cacheable."""
        if self.cache_control:
            return 'no-cache' not in self.cache_control.lower()
        return self.etag is not None or self.last_modified is not None

    @property
    def has_security_headers(self) -> bool:
        """Check if response has common security headers."""
        security_header_names = {
            'strict-transport-security',
            'content-security-policy',
            'x-frame-options',
            'x-content-type-options'
        }
        return bool(security_header_names.intersection(self.security_headers.keys()))


# Session persistence models

class SessionConfig(BaseModel):
    """Configuration for session persistence."""

    enable_cookies: bool = Field(default=True, description="Enable cookie handling")
    cookie_jar_path: Optional[Path] = Field(default=None, description="Path to save/load cookies")
    enable_auth_persistence: bool = Field(default=True, description="Persist authentication")
    session_timeout: int = Field(default=3600, ge=60, description="Session timeout in seconds")

    model_config = ConfigDict(
        validate_assignment=True
    )


@dataclass
class SessionData:
    """Data for session persistence."""

    cookies: Dict[str, Any]
    auth_headers: Dict[str, str]
    custom_headers: Dict[str, str]
    created_at: datetime
    last_used: datetime

    def is_expired(self, timeout_seconds: int) -> bool:
        """Check if session has expired."""
        return (datetime.now() - self.last_used).total_seconds() > timeout_seconds
