"""
Comprehensive exception handling for the web fetcher utility.

This module provides custom exceptions and error handling utilities for
robust network error management, timeout handling, and HTTP status code processing.
"""

from __future__ import annotations

import asyncio
from typing import Any, Dict, Optional, Type, Union

import aiohttp


class WebFetchError(Exception):
    """Base exception for all web fetching operations."""
    
    def __init__(self, message: str, url: Optional[str] = None, **kwargs):
        super().__init__(message)
        self.message = message
        self.url = url
        self.details = kwargs


class NetworkError(WebFetchError):
    """Raised for network-related errors."""
    pass


class TimeoutError(WebFetchError):
    """Raised when a request times out."""
    
    def __init__(self, message: str, url: Optional[str] = None, timeout_value: Optional[float] = None):
        super().__init__(message, url)
        self.timeout_value = timeout_value


class ConnectionError(WebFetchError):
    """Raised when connection fails."""
    pass


class HTTPError(WebFetchError):
    """Raised for HTTP-related errors."""
    
    def __init__(
        self, 
        message: str, 
        status_code: int, 
        url: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        response_text: Optional[str] = None
    ):
        super().__init__(message, url)
        self.status_code = status_code
        self.headers = headers or {}
        self.response_text = response_text


class ContentError(WebFetchError):
    """Raised when content parsing fails."""
    
    def __init__(
        self, 
        message: str, 
        url: Optional[str] = None,
        content_type: Optional[str] = None,
        content_length: Optional[int] = None
    ):
        super().__init__(message, url)
        self.content_type = content_type
        self.content_length = content_length


class RateLimitError(HTTPError):
    """Raised when rate limiting is encountered."""
    
    def __init__(
        self, 
        message: str, 
        url: Optional[str] = None,
        retry_after: Optional[Union[int, float]] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        super().__init__(message, 429, url, headers)
        self.retry_after = retry_after


class AuthenticationError(HTTPError):
    """Raised for authentication-related errors (401, 403)."""
    pass


class NotFoundError(HTTPError):
    """Raised when resource is not found (404)."""
    pass


class ServerError(HTTPError):
    """Raised for server errors (5xx)."""
    pass


class ErrorHandler:
    """
    Utility class for handling and categorizing different types of errors.
    
    Provides methods to convert aiohttp exceptions to custom exceptions
    and determine appropriate retry strategies.
    """
    
    @staticmethod
    def handle_aiohttp_error(
        error: Exception, 
        url: Optional[str] = None
    ) -> WebFetchError:
        """
        Convert aiohttp exceptions to custom WebFetchError subclasses.
        
        Args:
            error: The original aiohttp exception
            url: The URL that caused the error
            
        Returns:
            Appropriate WebFetchError subclass
        """
        if isinstance(error, asyncio.TimeoutError):
            return TimeoutError(
                f"Request timed out: {error}",
                url=url
            )
        
        elif isinstance(error, aiohttp.ClientTimeout):
            return TimeoutError(
                f"Client timeout: {error}",
                url=url,
                timeout_value=getattr(error, 'total', None)
            )
        
        elif isinstance(error, aiohttp.ClientConnectionError):
            return ConnectionError(
                f"Connection error: {error}",
                url=url
            )
        
        elif isinstance(error, aiohttp.ClientConnectorError):
            return ConnectionError(
                f"Connector error: {error}",
                url=url
            )
        
        elif isinstance(error, aiohttp.ClientSSLError):
            return ConnectionError(
                f"SSL error: {error}",
                url=url
            )
        
        elif isinstance(error, aiohttp.ClientPayloadError):
            return ContentError(
                f"Payload error: {error}",
                url=url
            )
        
        elif isinstance(error, aiohttp.ClientResponseError):
            return ErrorHandler.handle_http_status_error(
                error.status,
                str(error),
                url,
                getattr(error, 'headers', None)
            )
        
        else:
            return NetworkError(
                f"Unexpected network error: {error}",
                url=url
            )
    
    @staticmethod
    def handle_http_status_error(
        status_code: int,
        message: str,
        url: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        response_text: Optional[str] = None
    ) -> HTTPError:
        """
        Create appropriate HTTPError subclass based on status code.
        
        Args:
            status_code: HTTP status code
            message: Error message
            url: The URL that caused the error
            headers: Response headers
            response_text: Response body text
            
        Returns:
            Appropriate HTTPError subclass
        """
        if status_code == 401:
            return AuthenticationError(
                f"Authentication required: {message}",
                status_code,
                url,
                headers,
                response_text
            )
        
        elif status_code == 403:
            return AuthenticationError(
                f"Access forbidden: {message}",
                status_code,
                url,
                headers,
                response_text
            )
        
        elif status_code == 404:
            return NotFoundError(
                f"Resource not found: {message}",
                status_code,
                url,
                headers,
                response_text
            )
        
        elif status_code == 429:
            retry_after = None
            if headers:
                retry_after_header = headers.get('Retry-After') or headers.get('retry-after')
                if retry_after_header:
                    try:
                        retry_after = float(retry_after_header)
                    except ValueError:
                        pass
            
            return RateLimitError(
                f"Rate limit exceeded: {message}",
                url,
                retry_after,
                headers
            )
        
        elif 500 <= status_code < 600:
            return ServerError(
                f"Server error: {message}",
                status_code,
                url,
                headers,
                response_text
            )
        
        else:
            return HTTPError(
                message,
                status_code,
                url,
                headers,
                response_text
            )
    
    @staticmethod
    def is_retryable_error(error: Exception) -> bool:
        """
        Determine if an error is retryable.
        
        Args:
            error: The exception to check
            
        Returns:
            True if the error should be retried, False otherwise
        """
        # Network errors are generally retryable
        if isinstance(error, (NetworkError, ConnectionError, TimeoutError)):
            return True
        
        # Some HTTP errors are retryable
        if isinstance(error, HTTPError):
            # Server errors (5xx) are retryable
            if isinstance(error, ServerError):
                return True
            
            # Rate limiting is retryable with backoff
            if isinstance(error, RateLimitError):
                return True
            
            # Specific status codes that might be temporary
            if error.status_code in [408, 502, 503, 504]:
                return True
        
        # Content errors are generally not retryable
        if isinstance(error, ContentError):
            return False
        
        # Authentication errors are not retryable
        if isinstance(error, AuthenticationError):
            return False
        
        # 404 errors are not retryable
        if isinstance(error, NotFoundError):
            return False
        
        return False
    
    @staticmethod
    def get_retry_delay(error: Exception, attempt: int, base_delay: float = 1.0) -> float:
        """
        Calculate appropriate retry delay based on error type and attempt number.
        
        Args:
            error: The exception that occurred
            attempt: Current attempt number (0-based)
            base_delay: Base delay in seconds
            
        Returns:
            Delay in seconds before next retry
        """
        # Rate limiting has specific retry-after header
        if isinstance(error, RateLimitError) and error.retry_after:
            return error.retry_after
        
        # Exponential backoff for most retryable errors
        if ErrorHandler.is_retryable_error(error):
            return base_delay * (2 ** attempt)
        
        return 0.0
