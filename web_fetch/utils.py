"""
Utility functions for the web_fetch library.

This module provides additional utility functions including URL validation,
response analysis, caching, rate limiting, and session persistence.
"""

from __future__ import annotations

import asyncio
import gzip
import json
import pickle
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from urllib.parse import parse_qs, urljoin, urlparse, urlunparse
from weakref import WeakValueDictionary

import aiofiles

from .models import (
    CacheConfig,
    CacheEntry,
    HeaderAnalysis,
    RateLimitConfig,
    RateLimitState,
    SessionConfig,
    SessionData,
    URLAnalysis,
)


class URLValidator:
    """Utility class for URL validation and normalization."""
    
    # Common URL schemes
    VALID_SCHEMES = {'http', 'https', 'ftp', 'ftps'}
    
    # Regex patterns for validation
    DOMAIN_PATTERN = re.compile(
        r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
    )
    IP_PATTERN = re.compile(
        r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    )
    
    @classmethod
    def is_valid_url(cls, url: str) -> bool:
        """
        Check if a URL is valid.
        
        Args:
            url: URL string to validate
            
        Returns:
            True if URL is valid, False otherwise
        """
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme.lower() in cls.VALID_SCHEMES and
                bool(parsed.netloc) and
                cls._is_valid_domain(parsed.netloc.split(':')[0])
            )
        except Exception:
            return False
    
    @classmethod
    def _is_valid_domain(cls, domain: str) -> bool:
        """Check if domain is valid."""
        if not domain:
            return False

        # Check for localhost
        if domain.lower() in ('localhost', '::1'):
            return True

        # Check if it looks like an IP address (4 numbers separated by dots)
        if re.match(r'^\d+\.\d+\.\d+\.\d+$', domain):
            # If it looks like an IP, only validate it as an IP
            return cls.IP_PATTERN.match(domain) is not None

        # Check for domain name
        if cls.DOMAIN_PATTERN.match(domain):
            return True

        return False
    
    @classmethod
    def normalize_url(cls, url: str, base_url: Optional[str] = None) -> str:
        """
        Normalize a URL by resolving relative paths and cleaning up.
        
        Args:
            url: URL to normalize
            base_url: Base URL for resolving relative URLs
            
        Returns:
            Normalized URL string
        """
        # Handle relative URLs
        if base_url and not urlparse(url).scheme:
            url = urljoin(base_url, url)
        
        parsed = urlparse(url)
        
        # Normalize scheme to lowercase
        scheme = parsed.scheme.lower()
        
        # Normalize netloc (domain) to lowercase
        netloc = parsed.netloc.lower()
        
        # Clean up path
        path = parsed.path or '/'
        if path != '/' and path.endswith('/'):
            path = path.rstrip('/')
        
        # Sort query parameters for consistency
        query = parsed.query
        if query:
            params = parse_qs(query, keep_blank_values=True)
            sorted_params = sorted(params.items())
            query = '&'.join(f"{k}={'&'.join(v)}" for k, v in sorted_params)
        
        return urlunparse((scheme, netloc, path, parsed.params, query, parsed.fragment))
    
    @classmethod
    def analyze_url(cls, url: str) -> URLAnalysis:
        """
        Perform comprehensive URL analysis.
        
        Args:
            url: URL to analyze
            
        Returns:
            URLAnalysis object with detailed information
        """
        issues = []
        parsed = urlparse(url)
        
        # Basic validation
        is_valid = cls.is_valid_url(url)
        if not is_valid:
            issues.append("Invalid URL format")
        
        # Extract components
        scheme = parsed.scheme.lower()
        domain = parsed.netloc.split(':')[0].lower()
        port = parsed.port
        path = parsed.path or '/'
        query_params = dict(parse_qs(parsed.query, keep_blank_values=True))
        fragment = parsed.fragment
        
        # Security checks
        is_secure = scheme == 'https'
        if scheme == 'http':
            issues.append("URL uses insecure HTTP protocol")
        
        # Normalize URL
        normalized_url = cls.normalize_url(url) if is_valid else url
        
        return URLAnalysis(
            original_url=url,
            normalized_url=normalized_url,
            is_valid=is_valid,
            scheme=scheme,
            domain=domain,
            path=path,
            query_params=query_params,
            fragment=fragment,
            port=port,
            is_secure=is_secure,
            issues=issues
        )


class ResponseAnalyzer:
    """Utility class for analyzing HTTP responses."""
    
    SECURITY_HEADERS = {
        'strict-transport-security',
        'content-security-policy',
        'x-frame-options',
        'x-content-type-options',
        'x-xss-protection',
        'referrer-policy'
    }
    
    @classmethod
    def analyze_headers(cls, headers: Dict[str, str]) -> HeaderAnalysis:
        """
        Analyze HTTP response headers.
        
        Args:
            headers: Dictionary of response headers
            
        Returns:
            HeaderAnalysis object with parsed header information
        """
        # Normalize header names to lowercase
        normalized_headers = {k.lower(): v for k, v in headers.items()}
        
        # Extract common headers
        content_type = normalized_headers.get('content-type')
        content_length = None
        if 'content-length' in normalized_headers:
            try:
                content_length = int(normalized_headers['content-length'])
            except ValueError:
                pass
        
        content_encoding = normalized_headers.get('content-encoding')
        server = normalized_headers.get('server')
        cache_control = normalized_headers.get('cache-control')
        etag = normalized_headers.get('etag')
        last_modified = normalized_headers.get('last-modified')
        expires = normalized_headers.get('expires')
        
        # Extract security headers
        security_headers = {
            k: v for k, v in normalized_headers.items()
            if k in cls.SECURITY_HEADERS
        }
        
        # Extract custom headers (non-standard)
        standard_headers = {
            'content-type', 'content-length', 'content-encoding', 'server',
            'cache-control', 'etag', 'last-modified', 'expires', 'date',
            'connection', 'transfer-encoding', 'location', 'set-cookie'
        }
        custom_headers = {
            k: v for k, v in normalized_headers.items()
            if k not in standard_headers and k not in cls.SECURITY_HEADERS
        }
        
        return HeaderAnalysis(
            content_type=content_type,
            content_length=content_length,
            content_encoding=content_encoding,
            server=server,
            cache_control=cache_control,
            etag=etag,
            last_modified=last_modified,
            expires=expires,
            security_headers=security_headers,
            custom_headers=custom_headers
        )
    
    @classmethod
    def detect_content_type(cls, headers: Dict[str, str], content: bytes) -> str:
        """
        Detect content type from headers and content.
        
        Args:
            headers: Response headers
            content: Response content bytes
            
        Returns:
            Detected content type string
        """
        # Check Content-Type header first
        content_type = headers.get('content-type', '').lower()
        if content_type:
            return content_type.split(';')[0].strip()
        
        # Try to detect from content
        if not content:
            return 'application/octet-stream'
        
        # Check for common file signatures
        if content.startswith(b'<!DOCTYPE html') or content.startswith(b'<html'):
            return 'text/html'
        elif content.startswith(b'{') or content.startswith(b'['):
            try:
                json.loads(content.decode('utf-8'))
                return 'application/json'
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
        elif content.startswith(b'<?xml'):
            return 'application/xml'
        elif content.startswith(b'\x89PNG'):
            return 'image/png'
        elif content.startswith(b'\xff\xd8\xff'):
            return 'image/jpeg'
        elif content.startswith(b'GIF8'):
            return 'image/gif'
        elif content.startswith(b'%PDF'):
            return 'application/pdf'
        
        # Try to decode as text
        try:
            content.decode('utf-8')
            return 'text/plain'
        except UnicodeDecodeError:
            return 'application/octet-stream'


class SimpleCache:
    """Simple in-memory cache for HTTP responses."""
    
    def __init__(self, config: CacheConfig):
        """Initialize cache with configuration."""
        self.config = config
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []
    
    def _cleanup_expired(self) -> None:
        """Remove expired entries from cache."""
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry.is_expired
        ]
        for key in expired_keys:
            self._remove_entry(key)
    
    def _remove_entry(self, key: str) -> None:
        """Remove entry from cache and access order."""
        if key in self._cache:
            del self._cache[key]
        if key in self._access_order:
            self._access_order.remove(key)
    
    def _evict_lru(self) -> None:
        """Evict least recently used entries if cache is full."""
        while len(self._cache) >= self.config.max_size and self._access_order:
            lru_key = self._access_order.pop(0)
            self._remove_entry(lru_key)
    
    def get(self, url: str) -> Optional[CacheEntry]:
        """
        Get cached entry for URL.
        
        Args:
            url: URL to look up
            
        Returns:
            CacheEntry if found and not expired, None otherwise
        """
        self._cleanup_expired()
        
        if url not in self._cache:
            return None
        
        entry = self._cache[url]
        if entry.is_expired:
            self._remove_entry(url)
            return None
        
        # Update access order
        if url in self._access_order:
            self._access_order.remove(url)
        self._access_order.append(url)
        
        return entry
    
    def put(self, url: str, response_data: Any, headers: Dict[str, str], status_code: int) -> None:
        """
        Store response in cache.
        
        Args:
            url: URL to cache
            response_data: Response data to cache
            headers: Response headers
            status_code: HTTP status code
        """
        self._cleanup_expired()
        self._evict_lru()
        
        # Compress data if enabled
        compressed = False
        if self.config.enable_compression and isinstance(response_data, (str, bytes)):
            try:
                if isinstance(response_data, str):
                    response_data = response_data.encode('utf-8')
                response_data = gzip.compress(response_data)
                compressed = True
            except Exception:
                pass  # Fall back to uncompressed
        
        entry = CacheEntry(
            url=url,
            response_data=response_data,
            headers=headers if self.config.cache_headers else {},
            status_code=status_code,
            timestamp=datetime.now(),
            ttl=timedelta(seconds=self.config.ttl_seconds),
            compressed=compressed
        )
        
        self._cache[url] = entry
        if url in self._access_order:
            self._access_order.remove(url)
        self._access_order.append(url)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
        self._access_order.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        return len(self._cache)


class RateLimiter:
    """Rate limiter using token bucket algorithm."""
    
    def __init__(self, config: RateLimitConfig):
        """Initialize rate limiter with configuration."""
        self.config = config
        self._global_state = RateLimitState(
            tokens=config.burst_size,
            last_update=datetime.now()
        )
        self._host_states: Dict[str, RateLimitState] = {}
    
    def _get_host_from_url(self, url: str) -> str:
        """Extract host from URL."""
        try:
            return urlparse(url).netloc.lower()
        except Exception:
            return 'unknown'
    
    async def acquire(self, url: str) -> None:
        """
        Acquire permission to make a request.
        
        Args:
            url: URL for the request (used for per-host limiting)
        """
        if self.config.per_host:
            host = self._get_host_from_url(url)
            if host not in self._host_states:
                self._host_states[host] = RateLimitState(
                    tokens=self.config.burst_size,
                    last_update=datetime.now()
                )
            state = self._host_states[host]
        else:
            state = self._global_state
        
        # Wait until we can make a request
        while not state.can_make_request(self.config):
            await asyncio.sleep(0.01)  # Small delay
        
        state.consume_token()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        return {
            'global_tokens': self._global_state.tokens,
            'global_requests': self._global_state.requests_made,
            'host_count': len(self._host_states),
            'host_stats': {
                host: {
                    'tokens': state.tokens,
                    'requests': state.requests_made
                }
                for host, state in self._host_states.items()
            }
        }
