{".class": "MypyFile", "_fullname": "email.message", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Charset": {".class": "SymbolTableNode", "cross_ref": "email.charset.Charset", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ContentManager": {".class": "SymbolTableNode", "cross_ref": "email.contentmanager.ContentManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmailMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.message.EmailMessage", "name": "EmailMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.message.EmailMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.message", "mro": ["email.message.EmailMessage", "email.message.MIMEPart", "email.message.Message", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MIMEPart": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.message.MIMEPart", "name": "MIMEPart", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.message", "mro": ["email.message.MIMEPart", "email.message.Message", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_alternative": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.add_alternative", "name": "add_alternative", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["email.contentmanager.ContentManager", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_alternative of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_attachment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.add_attachment", "name": "add_attachment", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["email.contentmanager.ContentManager", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_attachment of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_related": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.add_related", "name": "add_related", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["email.contentmanager.ContentManager", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_related of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "unixfrom", "maxheader<PERSON>", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.as_string", "name": "as_string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "unixfrom", "maxheader<PERSON>", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_string of MIMEPart", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.attach", "name": "attach", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "payload"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}]}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.clear_content", "name": "clear_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_content of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "preferencelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.get_body", "name": "get_body", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "preferencelist"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_body of MIMEPart", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.get_content", "name": "get_content", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["email.contentmanager.ContentManager", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content of MIMEPart", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_attachment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.is_attachment", "name": "is_attachment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_attachment of MIMEPart", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_attachments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.iter_attachments", "name": "iter_attachments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_attachments of MIMEPart", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}]}}}, "iter_parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.iter_parts", "name": "iter_parts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_parts of MIMEPart", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_alternative": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.make_alternative", "name": "make_alternative", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "boundary"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_alternative of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_mixed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.make_mixed", "name": "make_mixed", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "boundary"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_mixed of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_related": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.make_related", "name": "make_related", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "boundary"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_related of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.MIMEPart.set_content", "name": "set_content", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "content_manager", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["email.contentmanager.ContentManager", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_content of MIMEPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.MIMEPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "id": 1, "name": "_HeaderRegistryT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "id": 2, "name": "_HeaderRegistryParamT", "namespace": "email.message.MIMEPart", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.MIMEPart"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_HeaderRegistryT", "_HeaderRegistryParamT"], "typeddict_type": null}}, "MaybeNone": {".class": "SymbolTableNode", "cross_ref": "_typeshed.MaybeNone", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Message": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.message.Message", "name": "Message", "type_vars": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.message.Message", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.message", "mro": ["email.message.Message", "builtins.object"], "names": {".class": "SymbolTable", "__bytes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__bytes__", "name": "__bytes__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bytes__ of Message", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of Message", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of Message", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of Message", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "_name", "_value", "_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.add_header", "name": "add_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "_name", "_value", "_params"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "email._ParamsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_header of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "unixfrom", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.as_bytes", "name": "as_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "unixfrom", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_bytes of Message", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "unixfrom", "maxheader<PERSON>", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.as_string", "name": "as_string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "unixfrom", "maxheader<PERSON>", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_string of Message", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.attach", "name": "attach", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "payload"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email.message.Message.defects", "name": "defects", "type": {".class": "Instance", "args": ["email.errors.MessageDefect"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "del_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "param", "header", "requote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.del_param", "name": "del_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "param", "header", "requote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "del_param of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "epilogue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email.message.Message.epilogue", "name": "epilogue", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_all", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_all", "name": "get_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_all", "name": "get_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_all", "name": "get_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_all", "name": "get_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_all", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_boundary", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_boundary", "name": "get_boundary", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boundary of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_boundary", "name": "get_boundary", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boundary of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_boundary", "name": "get_boundary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boundary of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_boundary", "name": "get_boundary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boundary of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boundary of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boundary of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_boundary", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_charset", "name": "get_charset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charset of Message", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "email.message._CharsetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_charsets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_charsets", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_charsets", "name": "get_charsets", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charsets of Message", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_charsets", "name": "get_charsets", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charsets of Message", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_charsets", "name": "get_charsets", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charsets of Message", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_charsets", "name": "get_charsets", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charsets of Message", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charsets of Message", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charsets of Message", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_charsets", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_content_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_content_charset", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_content_charset", "name": "get_content_charset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_charset of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_content_charset", "name": "get_content_charset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_charset of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_content_charset", "name": "get_content_charset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_charset of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_content_charset", "name": "get_content_charset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_charset of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_charset of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_charset of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_content_charset", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_content_disposition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_content_disposition", "name": "get_content_disposition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_disposition of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_content_maintype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_content_maintype", "name": "get_content_maintype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_maintype of Message", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_content_subtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_content_subtype", "name": "get_content_subtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_subtype of Message", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_content_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_content_type", "name": "get_content_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_type of Message", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_default_type", "name": "get_default_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_type of Message", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_filename", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_filename", "name": "get_filename", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_filename", "name": "get_filename", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_filename", "name": "get_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_filename", "name": "get_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_filename", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_param", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_param", "name": "get_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_param of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email._ParamType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_param", "name": "get_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_param of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email._ParamType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_param", "name": "get_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_param of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email._ParamType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_param", "name": "get_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_param of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email._ParamType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "NoneType"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_param of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email._ParamType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "param", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_param of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email._ParamType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_param", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_params", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_params", "name": "get_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_params of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_params", "name": "get_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_params of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_params", "name": "get_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_params of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_params", "name": "get_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_params of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_params of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "header", "unquote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_params of Message", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "id": -1, "name": "_T", "namespace": "email.message.Message.get_params", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "get_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.get_payload", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "i", "decode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "i", "decode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3], "arg_names": ["self", "i", "decode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._EncodedPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._EncodedPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._EncodedPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.get_payload", "name": "get_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._EncodedPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 3], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._EncodedPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "i", "decode"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_payload of Message", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._EncodedPayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_unixfrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.get_unixfrom", "name": "get_unixfrom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unixfrom of Message", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.is_multipart", "name": "is_multipart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multipart of Message", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of Message", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of Message", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email.message.Message.policy", "name": "policy", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}}}, "preamble": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email.message.Message.preamble", "name": "preamble", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "raw_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.raw_items", "name": "raw_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_items of Message", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "_name", "_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.replace_header", "name": "replace_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_name", "_value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_header of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_boundary", "name": "set_boundary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "boundary"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_boundary of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_charset", "name": "set_charset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._CharsetType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_charset of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_default_type", "name": "set_default_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ctype"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_type of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "param", "value", "header", "requote", "charset", "language", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_param", "name": "set_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "param", "value", "header", "requote", "charset", "language", "replace"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_param of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "email.message.Message.set_payload", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "payload", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.set_payload", "name": "set_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "payload", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": ["email.message._SupportsDecodeToPayload", {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.set_payload", "name": "set_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "payload", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": ["email.message._SupportsDecodeToPayload", {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "payload", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "email.message.Message.set_payload", "name": "set_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "payload", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": ["email.message._SupportsEncodeToPayload", "email.message._SupportsDecodeToPayload", {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["email.charset.Charset", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "email.message.Message.set_payload", "name": "set_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "payload", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": ["email.message._SupportsEncodeToPayload", "email.message._SupportsDecodeToPayload", {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["email.charset.Charset", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "payload", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": ["email.message._SupportsDecodeToPayload", {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "payload", "charset"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": ["email.message._SupportsEncodeToPayload", "email.message._SupportsDecodeToPayload", {".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["email.charset.Charset", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_payload of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_raw", "name": "set_raw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_raw of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "type", "header", "requote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_type", "name": "set_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "type", "header", "requote"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_type of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_unixfrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "unixfrom"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.set_unixfrom", "name": "set_unixfrom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unixfrom"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_unixfrom of Message", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.values", "name": "values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "values of Message", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "walk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message.Message.walk", "name": "walk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.Message.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "walk of Message", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.Message.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.Message.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message.Message.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderT", "id": 1, "name": "_HeaderT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "id": 2, "name": "_HeaderParamT", "namespace": "email.message.Message", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_HeaderT", "_HeaderParamT"], "typeddict_type": null}}, "MessageDefect": {".class": "SymbolTableNode", "cross_ref": "email.errors.MessageDefect", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Policy": {".class": "SymbolTableNode", "cross_ref": "email._policybase.Policy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CharsetType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "email.message._CharsetType", "line": 25, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["email.charset.Charset", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_EncodedPayloadType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "email.message._EncodedPayloadType", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.bytes"], "uses_pep604_syntax": true}}}, "_HeaderParamT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": "builtins.str", "fullname": "email.message._HeaderParamT", "name": "_HeaderParamT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_HeaderRegistryParamT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryParamT", "name": "_HeaderRegistryParamT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_HeaderRegistryT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "email.message._HeaderRegistryT", "name": "_HeaderRegistryT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_HeaderT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": "builtins.str", "fullname": "email.message._HeaderT", "name": "_HeaderT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_MultipartPayloadType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "email.message._MultipartPayloadType", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_ParamType": {".class": "SymbolTableNode", "cross_ref": "email._ParamType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ParamsType": {".class": "SymbolTableNode", "cross_ref": "email._ParamsType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_PayloadType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "email.message._PayloadType", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "builtins.str"], "uses_pep604_syntax": true}}}, "_SupportsDecodeToPayload": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.message._SupportsDecodeToPayload", "name": "_SupportsDecodeToPayload", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "email.message._SupportsDecodeToPayload", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "email.message", "mro": ["email.message._SupportsDecodeToPayload", "builtins.object"], "names": {".class": "SymbolTable", "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message._SupportsDecodeToPayload.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["email.message._SupportsDecodeToPayload", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of _SupportsDecodeToPayload", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SupportsEncodeToPayload": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.message._SupportsEncodeToPayload", "name": "_SupportsEncodeToPayload", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "email.message._SupportsEncodeToPayload", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "email.message", "mro": ["email.message._SupportsEncodeToPayload", "builtins.object"], "names": {".class": "SymbolTable", "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.message._SupportsEncodeToPayload.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["email.message._SupportsEncodeToPayload", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of _SupportsEncodeToPayload", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "email.message._PayloadType"}, {".class": "TypeAliasType", "args": [], "type_ref": "email.message._MultipartPayloadType"}, "email.message._SupportsDecodeToPayload"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.message._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email.message.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.message.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.message.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.message.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.message.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.message.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.message.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/email/message.pyi"}