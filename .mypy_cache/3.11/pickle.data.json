{".class": "MypyFile", "_fullname": "pickle", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ADDITEMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.ADDITEMS", "name": "ADDITEMS", "type": "builtins.bytes"}}, "APPEND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.APPEND", "name": "APPEND", "type": "builtins.bytes"}}, "APPENDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.APPENDS", "name": "APPENDS", "type": "builtins.bytes"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BINBYTES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINBYTES", "name": "BINBYTES", "type": "builtins.bytes"}}, "BINBYTES8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINBYTES8", "name": "BINBYTES8", "type": "builtins.bytes"}}, "BINFLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINFLOAT", "name": "BINFLOAT", "type": "builtins.bytes"}}, "BINGET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINGET", "name": "BINGET", "type": "builtins.bytes"}}, "BININT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BININT", "name": "BININT", "type": "builtins.bytes"}}, "BININT1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BININT1", "name": "BININT1", "type": "builtins.bytes"}}, "BININT2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BININT2", "name": "BININT2", "type": "builtins.bytes"}}, "BINPERSID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINPERSID", "name": "BINPERSID", "type": "builtins.bytes"}}, "BINPUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINPUT", "name": "BINPUT", "type": "builtins.bytes"}}, "BINSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINSTRING", "name": "BINSTRING", "type": "builtins.bytes"}}, "BINUNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINUNICODE", "name": "BINUNICODE", "type": "builtins.bytes"}}, "BINUNICODE8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BINUNICODE8", "name": "BINUNICODE8", "type": "builtins.bytes"}}, "BUILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BUILD", "name": "BUILD", "type": "builtins.bytes"}}, "BYTEARRAY8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.BYTEARRAY8", "name": "BYTEARRAY8", "type": "builtins.bytes"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFAULT_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.DEFAULT_PROTOCOL", "name": "DEFAULT_PROTOCOL", "type": "builtins.int"}}, "DICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.DICT", "name": "DICT", "type": "builtins.bytes"}}, "DUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.DUP", "name": "DUP", "type": "builtins.bytes"}}, "EMPTY_DICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EMPTY_DICT", "name": "EMPTY_DICT", "type": "builtins.bytes"}}, "EMPTY_LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EMPTY_LIST", "name": "EMPTY_LIST", "type": "builtins.bytes"}}, "EMPTY_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EMPTY_SET", "name": "EMPTY_SET", "type": "builtins.bytes"}}, "EMPTY_TUPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EMPTY_TUPLE", "name": "EMPTY_TUPLE", "type": "builtins.bytes"}}, "EXT1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EXT1", "name": "EXT1", "type": "builtins.bytes"}}, "EXT2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EXT2", "name": "EXT2", "type": "builtins.bytes"}}, "EXT4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.EXT4", "name": "EXT4", "type": "builtins.bytes"}}, "FALSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.FALSE", "name": "FALSE", "type": "builtins.bytes"}}, "FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.FLOAT", "name": "FLOAT", "type": "builtins.bytes"}}, "FRAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.FRAME", "name": "FRAME", "type": "builtins.bytes"}}, "FROZENSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.FROZENSET", "name": "FROZENSET", "type": "builtins.bytes"}}, "GET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.GET", "name": "GET", "type": "builtins.bytes"}}, "GLOBAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.GLOBAL", "name": "GLOBAL", "type": "builtins.bytes"}}, "HIGHEST_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.HIGHEST_PROTOCOL", "name": "HIGHEST_PROTOCOL", "type": "builtins.int"}}, "INST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.INST", "name": "INST", "type": "builtins.bytes"}}, "INT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.INT", "name": "INT", "type": "builtins.bytes"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.LIST", "name": "LIST", "type": "builtins.bytes"}}, "LONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.LONG", "name": "LONG", "type": "builtins.bytes"}}, "LONG1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.LONG1", "name": "LONG1", "type": "builtins.bytes"}}, "LONG4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.LONG4", "name": "LONG4", "type": "builtins.bytes"}}, "LONG_BINGET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.LONG_BINGET", "name": "LONG_BINGET", "type": "builtins.bytes"}}, "LONG_BINPUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.LONG_BINPUT", "name": "LONG_BINPUT", "type": "builtins.bytes"}}, "MARK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.MARK", "name": "MARK", "type": "builtins.bytes"}}, "MEMOIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.MEMOIZE", "name": "MEMOIZE", "type": "builtins.bytes"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NEWFALSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.NEWFALSE", "name": "NEWFALSE", "type": "builtins.bytes"}}, "NEWOBJ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.NEWOBJ", "name": "NEWOBJ", "type": "builtins.bytes"}}, "NEWOBJ_EX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.NEWOBJ_EX", "name": "NEWOBJ_EX", "type": "builtins.bytes"}}, "NEWTRUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.NEWTRUE", "name": "NEWTRUE", "type": "builtins.bytes"}}, "NEXT_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.NEXT_BUFFER", "name": "NEXT_BUFFER", "type": "builtins.bytes"}}, "NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.NONE", "name": "NONE", "type": "builtins.bytes"}}, "OBJ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.OBJ", "name": "OBJ", "type": "builtins.bytes"}}, "PERSID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.PERSID", "name": "PERSID", "type": "builtins.bytes"}}, "POP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.POP", "name": "POP", "type": "builtins.bytes"}}, "POP_MARK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.POP_MARK", "name": "POP_MARK", "type": "builtins.bytes"}}, "PROTO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.PROTO", "name": "PROTO", "type": "builtins.bytes"}}, "PUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.PUT", "name": "PUT", "type": "builtins.bytes"}}, "PickleBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pickle.Pickle<PERSON>uffer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "pickle.Pickle<PERSON>uffer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pickle", "mro": ["pickle.Pickle<PERSON>uffer", "builtins.object"], "names": {".class": "SymbolTable", "__buffer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle.PickleBuffer.__buffer__", "name": "__buffer__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pickle.Pickle<PERSON>uffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__buffer__ of PickleBuffer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "pickle.PickleBuffer.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "buffer"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pickle.PickleBuffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pickle.Pickle<PERSON>uffer", "values": [], "variance": 0}}, "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of PickleBuffer", "ret_type": "pickle.Pickle<PERSON>uffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pickle.PickleBuffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pickle.Pickle<PERSON>uffer", "values": [], "variance": 0}]}}}, "__release_buffer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle.PickleBuffer.__release_buffer__", "name": "__release_buffer__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pickle.Pickle<PERSON>uffer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__release_buffer__ of PickleBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle.PickleBuffer.raw", "name": "raw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pickle.Pickle<PERSON>uffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw of PickleBuffer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle.PickleBuffer.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pickle.Pickle<PERSON>uffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of <PERSON><PERSON>Buffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pickle.PickleBuffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pickle.Pickle<PERSON>uffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PickleError": {".class": "SymbolTableNode", "cross_ref": "_pickle.PickleError", "kind": "Gdef"}, "Pickler": {".class": "SymbolTableNode", "cross_ref": "_pickle.Pickler", "kind": "Gdef"}, "PicklingError": {".class": "SymbolTableNode", "cross_ref": "_pickle.PicklingError", "kind": "Gdef"}, "READONLY_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.READONLY_BUFFER", "name": "READONLY_BUFFER", "type": "builtins.bytes"}}, "REDUCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.REDUCE", "name": "REDUCE", "type": "builtins.bytes"}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SETITEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.SETITEM", "name": "SETITEM", "type": "builtins.bytes"}}, "SETITEMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.SETITEMS", "name": "SETITEMS", "type": "builtins.bytes"}}, "SHORT_BINBYTES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.SHORT_BINBYTES", "name": "SHORT_BINBYTES", "type": "builtins.bytes"}}, "SHORT_BINSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.SHORT_BINSTRING", "name": "SHORT_BINSTRING", "type": "builtins.bytes"}}, "SHORT_BINUNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.SHORT_BINUNICODE", "name": "SHORT_BINUNICODE", "type": "builtins.bytes"}}, "STACK_GLOBAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.STACK_GLOBAL", "name": "STACK_GLOBAL", "type": "builtins.bytes"}}, "STOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.STOP", "name": "STOP", "type": "builtins.bytes"}}, "STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.STRING", "name": "STRING", "type": "builtins.bytes"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsBytes": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsBytes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsWrite": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsWrite", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TRUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.TRUE", "name": "TRUE", "type": "builtins.bytes"}}, "TUPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.TUPLE", "name": "TUPLE", "type": "builtins.bytes"}}, "TUPLE1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.TUPLE1", "name": "TUPLE1", "type": "builtins.bytes"}}, "TUPLE2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.TUPLE2", "name": "TUPLE2", "type": "builtins.bytes"}}, "TUPLE3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.TUPLE3", "name": "TUPLE3", "type": "builtins.bytes"}}, "UNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.UNICODE", "name": "UNICODE", "type": "builtins.bytes"}}, "Unpickler": {".class": "SymbolTableNode", "cross_ref": "_pickle.Unpickler", "kind": "Gdef"}, "UnpicklingError": {".class": "SymbolTableNode", "cross_ref": "_pickle.UnpicklingError", "kind": "Gdef"}, "_BufferCallback": {".class": "SymbolTableNode", "cross_ref": "_pickle._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Pickler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pickle._Pickler", "name": "_Pickler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pickle._Pickler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pickle", "mro": ["pickle._Pickler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "file", "protocol", "fix_imports", "buffer_callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Pickler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "file", "protocol", "fix_imports", "buffer_callback"], "arg_types": ["pickle._Pickler", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsWrite"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "_pickle._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _<PERSON>ler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pickle._Pickler.bin", "name": "bin", "type": "builtins.bool"}}, "clear_memo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Pickler.clear_memo", "name": "clear_memo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pickle._Pickler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_memo of _<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pickle._Pickler.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["builtins.type", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_pickle.Unpickler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "dispatch_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pickle._Pickler.dispatch_table", "name": "dispatch_table", "type": {".class": "Instance", "args": ["builtins.type", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pickle._ReducedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Pickler.dump", "name": "dump", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pickle._Pickler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump of _Pickler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pickle._Pickler.fast", "name": "fast", "type": "builtins.bool"}}, "persistent_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Pickler.persistent_id", "name": "persistent_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pickle._Pickler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_id of _Pickler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reducer_override": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pickle._Pickler.reducer_override", "name": "reducer_override", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pickle._Pickler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pickle._Pickler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ReadableFileobj": {".class": "SymbolTableNode", "cross_ref": "_pickle._ReadableFileobj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ReducedType": {".class": "SymbolTableNode", "cross_ref": "_pickle._ReducedType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Unpickler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pickle._Unpickler", "name": "_Unpickler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pickle._Unpickler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pickle", "mro": ["pickle._Unpickler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "file", "fix_imports", "encoding", "errors", "buffers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Unpickler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "file", "fix_imports", "encoding", "errors", "buffers"], "arg_types": ["pickle._Unpickler", "_pickle._ReadableFileobj", "builtins.bool", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Unpickler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pickle._Unpickler.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["builtins.int", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pickle.Unpickler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "find_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Unpickler.find_class", "name": "find_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "name"], "arg_types": ["pickle._Unpickler", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_class of _Unpickler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Unpickler.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pickle._Unpickler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of _Unpickler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "persistent_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle._Unpickler.persistent_load", "name": "persistent_load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["pickle._Unpickler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_load of _Unpickler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pickle._Unpickler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pickle._Unpickler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pickle.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bytes_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pickle.bytes_types", "name": "bytes_types", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "decode_long": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle.decode_long", "name": "decode_long", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["typing.SupportsIndex"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "typing.SupportsBytes", "typing_extensions.Buffer"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode_long", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump": {".class": "SymbolTableNode", "cross_ref": "_pickle.dump", "kind": "Gdef"}, "dumps": {".class": "SymbolTableNode", "cross_ref": "_pickle.dumps", "kind": "Gdef"}, "encode_long": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pickle.encode_long", "name": "encode_long", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_long", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load": {".class": "SymbolTableNode", "cross_ref": "_pickle.load", "kind": "Gdef"}, "loads": {".class": "SymbolTableNode", "cross_ref": "_pickle.loads", "kind": "Gdef"}}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/pickle.pyi"}