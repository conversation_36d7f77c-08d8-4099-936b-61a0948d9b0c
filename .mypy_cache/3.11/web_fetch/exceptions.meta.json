{"data_mtime": 1754279388, "dep_lines": [8, 10, 11, 1, 1, 1, 13], "dep_prios": [5, 10, 5, 5, 30, 30, 10], "dependencies": ["__future__", "asyncio", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "35b78e65d9e3f3084ac8dec7c1b67028294e0f57", "id": "web_fetch.exceptions", "ignore_all": true, "interface_hash": "73e3e41b5d0210231004456df5bf268362d8dec0", "mtime": 1754276959, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/web-fetch/web_fetch/exceptions.py", "plugin_data": null, "size": 9469, "suppressed": ["aiohttp"], "version_id": "1.15.0"}