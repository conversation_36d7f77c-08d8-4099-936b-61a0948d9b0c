{".class": "MypyFile", "_fullname": "web_fetch.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AuthenticationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.AuthenticationError", "name": "AuthenticationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.AuthenticationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.AuthenticationError", "web_fetch.exceptions.HTTPError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.AuthenticationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.AuthenticationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.WebFetchError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.ConnectionError", "name": "ConnectionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.ConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.ConnectionError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.ConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.ConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.WebFetchError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.ContentError", "name": "ContentError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.ContentError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.ContentError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "message", "url", "content_type", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.ContentError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "message", "url", "content_type", "content_length"], "arg_types": ["web_fetch.exceptions.ContentError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ContentError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.ContentError.content_length", "name": "content_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "content_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.ContentError.content_type", "name": "content_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.ContentError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.ContentError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ErrorHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.ErrorHandler", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.ErrorHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.ErrorHandler", "builtins.object"], "names": {".class": "SymbolTable", "get_retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["error", "attempt", "base_delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "web_fetch.exceptions.ErrorHandler.get_retry_delay", "name": "get_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["error", "attempt", "base_delay"], "arg_types": ["builtins.Exception", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_retry_delay of Error<PERSON>andler", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.exceptions.ErrorHandler.get_retry_delay", "name": "get_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["error", "attempt", "base_delay"], "arg_types": ["builtins.Exception", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_retry_delay of Error<PERSON>andler", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "handle_aiohttp_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["error", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "web_fetch.exceptions.ErrorHandler.handle_aiohttp_error", "name": "handle_aiohttp_error", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["error", "url"], "arg_types": ["builtins.Exception", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_aiohttp_error of ErrorHandler", "ret_type": "web_fetch.exceptions.WebFetchError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.exceptions.ErrorHandler.handle_aiohttp_error", "name": "handle_aiohttp_error", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["error", "url"], "arg_types": ["builtins.Exception", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_aiohttp_error of ErrorHandler", "ret_type": "web_fetch.exceptions.WebFetchError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "handle_http_status_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["status_code", "message", "url", "headers", "response_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "web_fetch.exceptions.ErrorHandler.handle_http_status_error", "name": "handle_http_status_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["status_code", "message", "url", "headers", "response_text"], "arg_types": ["builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_http_status_error of ErrorHandler", "ret_type": "web_fetch.exceptions.HTTPError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.exceptions.ErrorHandler.handle_http_status_error", "name": "handle_http_status_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["status_code", "message", "url", "headers", "response_text"], "arg_types": ["builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_http_status_error of ErrorHandler", "ret_type": "web_fetch.exceptions.HTTPError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_retryable_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "web_fetch.exceptions.ErrorHandler.is_retryable_error", "name": "is_retryable_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["error"], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_retryable_error of <PERSON>rror<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.exceptions.ErrorHandler.is_retryable_error", "name": "is_retryable_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["error"], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_retryable_error of <PERSON>rror<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.ErrorHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.ErrorHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.WebFetchError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.HTTPError", "name": "HTTPError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.HTTPError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.HTTPError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "message", "status_code", "url", "headers", "response_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.HTTPError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "message", "status_code", "url", "headers", "response_text"], "arg_types": ["web_fetch.exceptions.HTTPError", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.HTTPError.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "response_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.HTTPError.response_text", "name": "response_text", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.HTTPError.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.HTTPError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.HTTPError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NetworkError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.WebFetchError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.NetworkError", "name": "NetworkError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.NetworkError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.NetworkError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.NetworkError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.NetworkError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.NotFoundError", "name": "NotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.NotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.NotFoundError", "web_fetch.exceptions.HTTPError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.NotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.NotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RateLimitError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.RateLimitError", "name": "RateLimitError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.RateLimitError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.RateLimitError", "web_fetch.exceptions.HTTPError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "message", "url", "retry_after", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.RateLimitError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "message", "url", "retry_after", "headers"], "arg_types": ["web_fetch.exceptions.RateLimitError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RateLimitError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retry_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.RateLimitError.retry_after", "name": "retry_after", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.RateLimitError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.RateLimitError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.ServerError", "name": "ServerError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.ServerError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.ServerError", "web_fetch.exceptions.HTTPError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.ServerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.ServerError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.exceptions.WebFetchError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.TimeoutError", "name": "TimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.TimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.TimeoutError", "web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "url", "timeout_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.TimeoutError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "url", "timeout_value"], "arg_types": ["web_fetch.exceptions.TimeoutError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimeoutError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.TimeoutError.timeout_value", "name": "timeout_value", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.TimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.TimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WebFetchError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.exceptions.WebFetchError", "name": "WebFetchError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.WebFetchError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.exceptions", "mro": ["web_fetch.exceptions.WebFetchError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "message", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.exceptions.WebFetchError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "message", "url", "kwargs"], "arg_types": ["web_fetch.exceptions.WebFetchError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebFetchError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "details": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.WebFetchError.details", "name": "details", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.WebFetchError.message", "name": "message", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.exceptions.WebFetchError.url", "name": "url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.exceptions.WebFetchError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.exceptions.WebFetchError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aiohttp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.exceptions.aiohttp", "name": "aiohttp", "type": {".class": "AnyType", "missing_import_name": "web_fetch.exceptions.aiohttp", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}}, "path": "/home/<USER>/web-fetch/web_fetch/exceptions.py"}