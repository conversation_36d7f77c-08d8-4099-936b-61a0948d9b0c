{".class": "MypyFile", "_fullname": "web_fetch.utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CacheConfig": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.CacheConfig", "kind": "Gdef"}, "CacheEntry": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.CacheEntry", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HeaderAnalysis": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.HeaderAnalysis", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "RateLimitConfig": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.RateLimitConfig", "kind": "Gdef"}, "RateLimitState": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.RateLimitState", "kind": "Gdef"}, "RateLimiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.utils.RateLimiter", "name": "RateLimiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.utils.RateLimiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.utils", "mro": ["web_fetch.utils.RateLimiter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.RateLimiter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["web_fetch.utils.RateLimiter", "web_fetch.models.RateLimitConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RateLimiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_host_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.RateLimiter._get_host_from_url", "name": "_get_host_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["web_fetch.utils.RateLimiter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_host_from_url of RateLimiter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_global_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.utils.RateLimiter._global_state", "name": "_global_state", "type": "web_fetch.models.RateLimitState"}}, "_host_states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.RateLimiter._host_states", "name": "_host_states", "type": {".class": "Instance", "args": ["builtins.str", "web_fetch.models.RateLimitState"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "acquire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.utils.RateLimiter.acquire", "name": "acquire", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["web_fetch.utils.RateLimiter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acquire of RateLimiter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.utils.RateLimiter.config", "name": "config", "type": "web_fetch.models.RateLimitConfig"}}, "get_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.RateLimiter.get_stats", "name": "get_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.utils.RateLimiter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats of RateLimiter", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.utils.RateLimiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.utils.RateLimiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseAnalyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.utils.ResponseAnalyzer", "name": "ResponseAnalyzer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.utils.ResponseAnalyzer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.utils", "mro": ["web_fetch.utils.ResponseAnalyzer", "builtins.object"], "names": {".class": "SymbolTable", "SECURITY_HEADERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "web_fetch.utils.ResponseAnalyzer.SECURITY_HEADERS", "name": "SECURITY_HEADERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "analyze_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "web_fetch.utils.ResponseAnalyzer.analyze_headers", "name": "analyze_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "headers"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.ResponseAnalyzer"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_headers of ResponseAnalyzer", "ret_type": "web_fetch.models.HeaderAnalysis", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.ResponseAnalyzer.analyze_headers", "name": "analyze_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "headers"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.ResponseAnalyzer"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_headers of ResponseAnalyzer", "ret_type": "web_fetch.models.HeaderAnalysis", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "detect_content_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "headers", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "web_fetch.utils.ResponseAnalyzer.detect_content_type", "name": "detect_content_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "headers", "content"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.ResponseAnalyzer"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_content_type of ResponseAnalyzer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.ResponseAnalyzer.detect_content_type", "name": "detect_content_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "headers", "content"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.ResponseAnalyzer"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_content_type of ResponseAnalyzer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.utils.ResponseAnalyzer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.utils.ResponseAnalyzer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionConfig": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.SessionConfig", "kind": "Gdef"}, "SessionData": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.SessionData", "kind": "Gdef"}, "SimpleCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.utils.SimpleCache", "name": "SimpleCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.utils", "mro": ["web_fetch.utils.SimpleCache", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["web_fetch.utils.SimpleCache", "web_fetch.models.CacheConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_access_order": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.SimpleCache._access_order", "name": "_access_order", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.SimpleCache._cache", "name": "_cache", "type": {".class": "Instance", "args": ["builtins.str", "web_fetch.models.CacheEntry"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_cleanup_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache._cleanup_expired", "name": "_cleanup_expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.utils.SimpleCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cleanup_expired of SimpleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evict_lru": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache._evict_lru", "name": "_evict_lru", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.utils.SimpleCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_evict_lru of SimpleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache._remove_entry", "name": "_remove_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["web_fetch.utils.SimpleCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_entry of SimpleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.utils.SimpleCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of SimpleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.utils.SimpleCache.config", "name": "config", "type": "web_fetch.models.CacheConfig"}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["web_fetch.utils.SimpleCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SimpleCache", "ret_type": {".class": "UnionType", "items": ["web_fetch.models.CacheEntry", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "url", "response_data", "headers", "status_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "url", "response_data", "headers", "status_code"], "arg_types": ["web_fetch.utils.SimpleCache", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put of SimpleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.utils.SimpleCache.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.utils.SimpleCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of SimpleCache", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.utils.SimpleCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.utils.SimpleCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "URLAnalysis": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.URLAnalysis", "kind": "Gdef"}, "URLValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.utils.URLValidator", "name": "URLValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.utils.URLValidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.utils", "mro": ["web_fetch.utils.URLValidator", "builtins.object"], "names": {".class": "SymbolTable", "DOMAIN_PATTERN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "web_fetch.utils.URLValidator.DOMAIN_PATTERN", "name": "DOMAIN_PATTERN", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "IP_PATTERN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "web_fetch.utils.URLValidator.IP_PATTERN", "name": "IP_PATTERN", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "VALID_SCHEMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "web_fetch.utils.URLValidator.VALID_SCHEMES", "name": "VALID_SCHEMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_is_valid_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "web_fetch.utils.URLValidator._is_valid_domain", "name": "_is_valid_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "domain"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_valid_domain of URLValidator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.URLValidator._is_valid_domain", "name": "_is_valid_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "domain"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_valid_domain of URLValidator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "analyze_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "web_fetch.utils.URLValidator.analyze_url", "name": "analyze_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_url of URLValidator", "ret_type": "web_fetch.models.URLAnalysis", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.URLValidator.analyze_url", "name": "analyze_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_url of URLValidator", "ret_type": "web_fetch.models.URLAnalysis", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_valid_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "web_fetch.utils.URLValidator.is_valid_url", "name": "is_valid_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_url of URLValidator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.URLValidator.is_valid_url", "name": "is_valid_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_url of URLValidator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "normalize_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "url", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "web_fetch.utils.URLValidator.normalize_url", "name": "normalize_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "url", "base_url"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_url of URLValidator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.URLValidator.normalize_url", "name": "normalize_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "url", "base_url"], "arg_types": [{".class": "TypeType", "item": "web_fetch.utils.URLValidator"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_url of URLValidator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.utils.URLValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.utils.URLValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WeakValueDictionary": {".class": "SymbolTableNode", "cross_ref": "weakref.<PERSON>ak<PERSON>alueDictionary", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aiofiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.utils.aiofiles", "name": "aiofiles", "type": {".class": "AnyType", "missing_import_name": "web_fetch.utils.aiofiles", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "gzip": {".class": "SymbolTableNode", "cross_ref": "gzip", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "parse_qs": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qs", "kind": "Gdef"}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "urlunparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunparse", "kind": "Gdef"}}, "path": "/home/<USER>/web-fetch/web_fetch/utils.py"}