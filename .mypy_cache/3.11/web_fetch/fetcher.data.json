{".class": "MypyFile", "_fullname": "web_fetch.fetcher", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef"}, "BatchFetchRequest": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.BatchFetchRequest", "kind": "Gdef"}, "BatchFetchResult": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.BatchFetchResult", "kind": "Gdef"}, "BeautifulSoup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.BeautifulSoup", "name": "BeautifulSoup", "type": {".class": "AnyType", "missing_import_name": "web_fetch.fetcher.BeautifulSoup", "source_any": null, "type_of_any": 3}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClientSession": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.ClientSession", "name": "ClientSession", "type": {".class": "AnyType", "missing_import_name": "web_fetch.fetcher.ClientSession", "source_any": null, "type_of_any": 3}}}, "ClientTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.ClientTimeout", "name": "ClientTimeout", "type": {".class": "AnyType", "missing_import_name": "web_fetch.fetcher.ClientTimeout", "source_any": null, "type_of_any": 3}}}, "ContentError": {".class": "SymbolTableNode", "cross_ref": "web_fetch.exceptions.ContentError", "kind": "Gdef"}, "ContentType": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.ContentType", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ErrorHandler": {".class": "SymbolTableNode", "cross_ref": "web_fetch.exceptions.ErrorHandler", "kind": "Gdef"}, "FetchConfig": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.FetchConfig", "kind": "Gdef"}, "FetchRequest": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.FetchRequest", "kind": "Gdef"}, "FetchResult": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.FetchResult", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "web_fetch.exceptions.HTTPError", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NetworkError": {".class": "SymbolTableNode", "cross_ref": "web_fetch.exceptions.NetworkError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ProgressInfo": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.ProgressInfo", "kind": "Gdef"}, "RateLimiter": {".class": "SymbolTableNode", "cross_ref": "web_fetch.utils.RateLimiter", "kind": "Gdef"}, "RetryStrategy": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.RetryStrategy", "kind": "Gdef"}, "SimpleCache": {".class": "SymbolTableNode", "cross_ref": "web_fetch.utils.SimpleCache", "kind": "Gdef"}, "StreamRequest": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.StreamRequest", "kind": "Gdef"}, "StreamResult": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.StreamResult", "kind": "Gdef"}, "StreamingConfig": {".class": "SymbolTableNode", "cross_ref": "web_fetch.models.StreamingConfig", "kind": "Gdef"}, "StreamingWebFetcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["web_fetch.fetcher.WebFetcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.fetcher.StreamingWebFetcher", "name": "StreamingWebFetcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.StreamingWebFetcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.fetcher", "mro": ["web_fetch.fetcher.StreamingWebFetcher", "web_fetch.fetcher.WebFetcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.StreamingWebFetcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["web_fetch.fetcher.StreamingWebFetcher", {".class": "UnionType", "items": ["web_fetch.models.FetchConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamingWebFetcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.StreamingWebFetcher._cache", "name": "_cache", "type": {".class": "UnionType", "items": ["web_fetch.utils.SimpleCache", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_rate_limiter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.StreamingWebFetcher._rate_limiter", "name": "_rate_limiter", "type": {".class": "UnionType", "items": ["web_fetch.utils.RateLimiter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "stream_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "request", "progress_callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.StreamingWebFetcher.stream_fetch", "name": "stream_fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "request", "progress_callback"], "arg_types": ["web_fetch.fetcher.StreamingWebFetcher", "web_fetch.models.StreamRequest", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["web_fetch.models.ProgressInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_fetch of StreamingWebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.StreamResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.fetcher.StreamingWebFetcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.fetcher.StreamingWebFetcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TCPConnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.TCPConnector", "name": "TCPConnector", "type": {".class": "AnyType", "missing_import_name": "web_fetch.fetcher.TCPConnector", "source_any": null, "type_of_any": 3}}}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "web_fetch.exceptions.TimeoutError", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WebFetchError": {".class": "SymbolTableNode", "cross_ref": "web_fetch.exceptions.WebFetchError", "kind": "Gdef"}, "WebFetcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_fetch.fetcher.WebFetcher", "name": "WebFetcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.WebFetcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_fetch.fetcher", "mro": ["web_fetch.fetcher.WebFetcher", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.fetcher.WebFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.fetcher.WebFetcher"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "arg_types": ["web_fetch.fetcher.WebFetcher", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.WebFetcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["web_fetch.fetcher.WebFetcher", {".class": "UnionType", "items": ["web_fetch.models.FetchConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebFetcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.WebFetcher._calculate_retry_delay", "name": "_calculate_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attempt"], "arg_types": ["web_fetch.fetcher.WebFetcher", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_retry_delay of WebFetcher", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher._create_session", "name": "_create_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.fetcher.WebFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_session of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher._execute_request", "name": "_execute_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "attempt"], "arg_types": ["web_fetch.fetcher.WebFetcher", "web_fetch.models.FetchRequest", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_request of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.FetchResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content_bytes", "requested_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher._parse_content", "name": "_parse_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content_bytes", "requested_type"], "arg_types": ["web_fetch.fetcher.WebFetcher", "builtins.bytes", "web_fetch.models.ContentType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_content of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_semaphore": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.WebFetcher._semaphore", "name": "_semaphore", "type": {".class": "UnionType", "items": ["asyncio.locks.Semaphore", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.WebFetcher._session", "name": "_session", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "web_fetch.fetcher.ClientSession", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.fetcher.WebFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_fetch.fetcher.WebFetcher.config", "name": "config", "type": "web_fetch.models.FetchConfig"}}, "fetch_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "batch_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher.fetch_batch", "name": "fetch_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "batch_request"], "arg_types": ["web_fetch.fetcher.WebFetcher", "web_fetch.models.BatchFetchRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_batch of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.BatchFetchResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.WebFetcher.fetch_single", "name": "fetch_single", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["web_fetch.fetcher.WebFetcher", "web_fetch.models.FetchRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_single of WebFetcher", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.FetchResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_decorated"], "fullname": "web_fetch.fetcher.WebFetcher.session_context", "name": "session_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.fetcher.WebFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_context of WebFetcher", "ret_type": {".class": "Instance", "args": ["web_fetch.fetcher.WebFetcher", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.WebFetcher.session_context", "name": "session_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["web_fetch.fetcher.WebFetcher"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_context of WebFetcher", "ret_type": {".class": "Instance", "args": ["web_fetch.fetcher.WebFetcher", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._AsyncGeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_fetch.fetcher.WebFetcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_fetch.fetcher.WebFetcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.fetcher.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.fetcher.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.fetcher.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.fetcher.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.fetcher.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_fetch.fetcher.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aiofiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.aiofiles", "name": "aiofiles", "type": {".class": "AnyType", "missing_import_name": "web_fetch.fetcher.aiofiles", "source_any": null, "type_of_any": 3}}}, "aiohttp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "web_fetch.fetcher.aiohttp", "name": "aiohttp", "type": {".class": "AnyType", "missing_import_name": "web_fetch.fetcher.aiohttp", "source_any": null, "type_of_any": 3}}}, "analyze_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.analyze_headers", "name": "analyze_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_headers", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.analyze_url", "name": "analyze_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_url", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asynccontextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.asynccontextmanager", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "detect_content_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["headers", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.detect_content_type", "name": "detect_content_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["headers", "content"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_content_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "download_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["url", "output_path", "chunk_size", "progress_callback", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.download_file", "name": "download_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["url", "output_path", "chunk_size", "progress_callback", "config"], "arg_types": ["builtins.str", "pathlib.Path", "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["web_fetch.models.ProgressInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["web_fetch.models.FetchConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_file", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.StreamResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["url", "content_type", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.fetch_url", "name": "fetch_url", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["url", "content_type", "config"], "arg_types": ["builtins.str", "web_fetch.models.ContentType", {".class": "UnionType", "items": ["web_fetch.models.FetchConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_url", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.FetchResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_urls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["urls", "content_type", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.fetch_urls", "name": "fetch_urls", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["urls", "content_type", "config"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "web_fetch.models.ContentType", {".class": "UnionType", "items": ["web_fetch.models.FetchConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_urls", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.BatchFetchResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_with_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["url", "content_type", "cache_config", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "web_fetch.fetcher.fetch_with_cache", "name": "fetch_with_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["url", "content_type", "cache_config", "config"], "arg_types": ["builtins.str", "web_fetch.models.ContentType", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["web_fetch.models.FetchConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_with_cache", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "web_fetch.models.FetchResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.is_valid_url", "name": "is_valid_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_url", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "normalize_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["url", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_fetch.fetcher.normalize_url", "name": "normalize_url", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["url", "base_url"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_url", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "/home/<USER>/web-fetch/web_fetch/fetcher.py"}