{"data_mtime": 1754281009, "dep_lines": [16, 28, 41, 58, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["web_fetch.exceptions", "web_fetch.fetcher", "web_fetch.models", "web_fetch.utils", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "ba6deaaa1fea11fff0cf23d57e1ab7a73023efbc", "id": "web_fetch", "ignore_all": true, "interface_hash": "2d2d47ec36eebbc6a2bd669d372dfa2287b4c773", "mtime": 1754277987, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/web-fetch/web_fetch/__init__.py", "plugin_data": null, "size": 2535, "suppressed": [], "version_id": "1.15.0"}