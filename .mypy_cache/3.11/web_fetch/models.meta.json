{"data_mtime": 1754280186, "dep_lines": [16, 8, 10, 11, 12, 13, 14, 15, 340, 1, 1, 1, 1, 1, 1, 18], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["urllib.parse", "__future__", "asyncio", "dataclasses", "datetime", "enum", "pathlib", "typing", "gzip", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing_extensions"], "hash": "3fae8e37f7f01d877a2ac94f9501acb344894144", "id": "web_fetch.models", "ignore_all": true, "interface_hash": "807516fbfebdaa62345efa8150eb914860a9e756", "mtime": 1754280174, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/web-fetch/web_fetch/models.py", "plugin_data": null, "size": 15639, "suppressed": ["pydantic"], "version_id": "1.15.0"}