{"data_mtime": 1754281009, "dep_lines": [23, 31, 44, 8, 10, 11, 12, 13, 14, 15, 16, 642, 1, 1, 1, 1, 1, 1, 1, 1, 18, 19, 21], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5], "dependencies": ["web_fetch.exceptions", "web_fetch.models", "web_fetch.utils", "__future__", "asyncio", "json", "time", "contextlib", "datetime", "pathlib", "typing", "gzip", "builtins", "_frozen_importlib", "abc", "asyncio.locks", "asyncio.mixins", "enum", "os", "typing_extensions"], "hash": "aa08f6182424a1b8e94207d4be8c1705fa23b300", "id": "web_fetch.fetcher", "ignore_all": true, "interface_hash": "e168f9ea79c112e4703accb2148e00361a9ab303", "mtime": 1754280951, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/web-fetch/web_fetch/fetcher.py", "plugin_data": null, "size": 25355, "suppressed": ["aiofiles", "aiohttp", "bs4"], "version_id": "1.15.0"}