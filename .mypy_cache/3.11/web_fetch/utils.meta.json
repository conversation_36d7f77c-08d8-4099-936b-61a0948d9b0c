{"data_mtime": 1754280186, "dep_lines": [18, 23, 8, 10, 11, 12, 13, 14, 15, 16, 17, 19, 1, 1, 1, 1, 21], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 10], "dependencies": ["urllib.parse", "web_fetch.models", "__future__", "asyncio", "gzip", "json", "pickle", "re", "datetime", "pathlib", "typing", "weakref", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "4683b5e514a07b3a7340c44dc85916debbf4fa19", "id": "web_fetch.utils", "ignore_all": true, "interface_hash": "43065567c6c3e0426f71a6bbf5039682e30ce0a2", "mtime": 1754280103, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/web-fetch/web_fetch/utils.py", "plugin_data": null, "size": 14682, "suppressed": ["aiofiles"], "version_id": "1.15.0"}