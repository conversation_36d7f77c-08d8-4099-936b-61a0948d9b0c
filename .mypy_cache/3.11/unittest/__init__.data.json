{".class": "MypyFile", "_fullname": "unittest", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseTestSuite": {".class": "SymbolTableNode", "cross_ref": "unittest.suite.BaseTestSuite", "kind": "Gdef", "module_public": false}, "FunctionTestCase": {".class": "SymbolTableNode", "cross_ref": "unittest.case.FunctionTestCase", "kind": "Gdef"}, "IsolatedAsyncioTestCase": {".class": "SymbolTableNode", "cross_ref": "unittest.async_case.IsolatedAsyncioTestCase", "kind": "Gdef"}, "SkipTest": {".class": "SymbolTableNode", "cross_ref": "unittest.case.SkipTest", "kind": "Gdef"}, "TestCase": {".class": "SymbolTableNode", "cross_ref": "unittest.case.TestCase", "kind": "Gdef"}, "TestLoader": {".class": "SymbolTableNode", "cross_ref": "unittest.loader.TestLoader", "kind": "Gdef"}, "TestProgram": {".class": "SymbolTableNode", "cross_ref": "unittest.main.TestProgram", "kind": "Gdef", "module_public": false}, "TestResult": {".class": "SymbolTableNode", "cross_ref": "unittest.result.TestResult", "kind": "Gdef"}, "TestSuite": {".class": "SymbolTableNode", "cross_ref": "unittest.suite.TestSuite", "kind": "Gdef"}, "TextTestResult": {".class": "SymbolTableNode", "cross_ref": "unittest.runner.TextTestResult", "kind": "Gdef"}, "TextTestRunner": {".class": "SymbolTableNode", "cross_ref": "unittest.runner.TextTestRunner", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "unittest.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__dir__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.__dir__", "name": "__dir__", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dir__", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "addModuleCleanup": {".class": "SymbolTableNode", "cross_ref": "unittest.case.addModuleCleanup", "kind": "Gdef"}, "defaultTestLoader": {".class": "SymbolTableNode", "cross_ref": "unittest.loader.defaultTestLoader", "kind": "Gdef"}, "doModuleCleanups": {".class": "SymbolTableNode", "cross_ref": "unittest.case.doModuleCleanups", "kind": "Gdef"}, "enterModuleContext": {".class": "SymbolTableNode", "cross_ref": "unittest.case.enterModuleContext", "kind": "Gdef"}, "expectedFailure": {".class": "SymbolTableNode", "cross_ref": "unittest.case.expectedFailure", "kind": "Gdef"}, "findTestCases": {".class": "SymbolTableNode", "cross_ref": "unittest.loader.findTestCases", "kind": "Gdef"}, "getTestCaseNames": {".class": "SymbolTableNode", "cross_ref": "unittest.loader.getTestCaseNames", "kind": "Gdef"}, "installHandler": {".class": "SymbolTableNode", "cross_ref": "unittest.signals.installHandler", "kind": "Gdef"}, "load_tests": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["loader", "tests", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.load_tests", "name": "load_tests", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["loader", "tests", "pattern"], "arg_types": ["unittest.loader.TestLoader", "unittest.suite.TestSuite", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_tests", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main": {".class": "SymbolTableNode", "cross_ref": "unittest.main.main", "kind": "Gdef"}, "makeSuite": {".class": "SymbolTableNode", "cross_ref": "unittest.loader.makeSuite", "kind": "Gdef"}, "registerResult": {".class": "SymbolTableNode", "cross_ref": "unittest.signals.registerResult", "kind": "Gdef"}, "removeHandler": {".class": "SymbolTableNode", "cross_ref": "unittest.signals.removeHandler", "kind": "Gdef"}, "removeResult": {".class": "SymbolTableNode", "cross_ref": "unittest.signals.removeResult", "kind": "Gdef"}, "skip": {".class": "SymbolTableNode", "cross_ref": "unittest.case.skip", "kind": "Gdef"}, "skipIf": {".class": "SymbolTableNode", "cross_ref": "unittest.case.skipIf", "kind": "Gdef"}, "skipUnless": {".class": "SymbolTableNode", "cross_ref": "unittest.case.skipUnless", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/unittest/__init__.pyi"}