{"data_mtime": 1754279388, "dep_lines": [3, 1, 2, 4, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "threading", "_typeshed", "logging", "typing", "builtins", "_frozen_importlib", "_thread", "abc", "types", "typing_extensions"], "hash": "20921e92e2faf6772f32bb1319a36d6ee1437f1d", "id": "multiprocessing.util", "ignore_all": true, "interface_hash": "d730829073ea789e0b1cb2223242261b90b50056", "mtime": 1754134419, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/multiprocessing/util.pyi", "plugin_data": null, "size": 2873, "suppressed": [], "version_id": "1.15.0"}