{".class": "MypyFile", "_fullname": "multiprocessing.popen_spawn_posix", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Finalize": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.util.Finalize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Popen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.popen_fork.Popen"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.popen_spawn_posix.Popen", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.popen_spawn_posix.Popen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.popen_spawn_posix", "mro": ["multiprocessing.popen_spawn_posix.Popen", "multiprocessing.popen_fork.Popen", "builtins.object"], "names": {".class": "SymbolTable", "DupFd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.popen_spawn_posix.Popen.DupFd", "name": "DupFd", "type": {".class": "TypeType", "item": "multiprocessing.popen_spawn_posix._DupFd"}}}, "finalizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.popen_spawn_posix.Popen.finalizer", "name": "finalizer", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.util.Finalize"}}}, "pid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.popen_spawn_posix.Popen.pid", "name": "pid", "type": "builtins.int"}}, "sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.popen_spawn_posix.Popen.sentinel", "name": "sentinel", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.popen_spawn_posix.Popen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.popen_spawn_posix.Popen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DupFd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.popen_spawn_posix._DupFd", "name": "_DupFd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.popen_spawn_posix._DupFd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.popen_spawn_posix", "mro": ["multiprocessing.popen_spawn_posix._DupFd", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.popen_spawn_posix._DupFd.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fd"], "arg_types": ["multiprocessing.popen_spawn_posix._DupFd", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _DupFd", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.popen_spawn_posix._DupFd.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.popen_spawn_posix._DupFd"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach of _DupFd", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.popen_spawn_posix._DupFd.fd", "name": "fd", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.popen_spawn_posix._DupFd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.popen_spawn_posix._DupFd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "multiprocessing.popen_spawn_posix.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.popen_spawn_posix.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.popen_spawn_posix.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.popen_spawn_posix.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.popen_spawn_posix.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.popen_spawn_posix.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.popen_spawn_posix.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "popen_fork": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.popen_fork", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/multiprocessing/popen_spawn_posix.pyi"}