{"data_mtime": 1754276105, "dep_lines": [3, 1, 2, 4, 20, 21, 22, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "sys", "_typeshed", "genericpath", "os", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "e8a6f0db23a10ee4e66ec15b39654e4de26d03e8", "id": "posixpath", "ignore_all": true, "interface_hash": "b3ede02018088ba9893619d9e74a35d580c97c1d", "mtime": 1754134419, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/posixpath.pyi", "plugin_data": null, "size": 4811, "suppressed": [], "version_id": "1.15.0"}